import {
  getGeographicalData,
  getResourceSiteRelations,
  getSiteDeviceRelations,
  getAvailableRooms
} from "@/api/base-config";

const state = {
  // 地理数据（省市区）
  geographicalData: null,
  
  // 资源-站点类型关联关系
  resourceSiteRelations: [],
  
  // 站点-设备类型关联关系
  siteDeviceRelations: [],
  
  // 可用房间数据
  availableRooms: {},
  
  // 加载状态
  loading: {
    geographicalData: false,
    resourceSiteRelations: false,
    siteDeviceRelations: false,
    availableRooms: false
  },
  
  // 错误信息
  errors: {
    geographicalData: null,
    resourceSiteRelations: null,
    siteDeviceRelations: null,
    availableRooms: null
  },
  
  // 数据加载时间戳
  timestamps: {
    geographicalData: 0,
    resourceSiteRelations: 0,
    siteDeviceRelations: 0,
    availableRooms: 0
  }
};

const mutations = {
  // 设置地理数据
  SET_GEOGRAPHICAL_DATA(state, data) {
    state.geographicalData = data;
    state.timestamps.geographicalData = Date.now();
  },
  
  // 设置资源-站点关联关系
  SET_RESOURCE_SITE_RELATIONS(state, data) {
    state.resourceSiteRelations = data;
    state.timestamps.resourceSiteRelations = Date.now();
  },
  
  // 设置站点-设备关联关系
  SET_SITE_DEVICE_RELATIONS(state, data) {
    state.siteDeviceRelations = data;
    state.timestamps.siteDeviceRelations = Date.now();
  },
  
  // 设置可用房间数据
  SET_AVAILABLE_ROOMS(state, data) {
    state.availableRooms = data;
    state.timestamps.availableRooms = Date.now();
  },
  
  // 设置加载状态
  SET_LOADING(state, { type, loading }) {
    state.loading[type] = loading;
  },
  
  // 设置错误信息
  SET_ERROR(state, { type, error }) {
    state.errors[type] = error;
  },
  
  // 清除错误信息
  CLEAR_ERROR(state, type) {
    state.errors[type] = null;
  },
  
  // 清除所有错误信息
  CLEAR_ALL_ERRORS(state) {
    Object.keys(state.errors).forEach(key => {
      state.errors[key] = null;
    });
  }
};

const actions = {
  // 加载地理数据
  async loadGeographicalData({ commit }, forceRefresh = false) {
    // 如果数据已存在且不强制刷新，则直接返回
    if (!forceRefresh && state.geographicalData) {
      return state.geographicalData;
    }
    
    commit("SET_LOADING", { type: "geographicalData", loading: true });
    commit("CLEAR_ERROR", "geographicalData");
    
    try {
      const response = await getGeographicalData();
      if (response.code === 0) {
        commit("SET_GEOGRAPHICAL_DATA", response.data);
        return response.data;
      } else {
        const error = response.msg || "加载地理数据失败";
        commit("SET_ERROR", { type: "geographicalData", error });
        throw new Error(error);
      }
    } catch (error) {
      const errorMsg = error.message || "加载地理数据失败";
      commit("SET_ERROR", { type: "geographicalData", error: errorMsg });
      console.error("加载地理数据失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", { type: "geographicalData", loading: false });
    }
  },
  
  // 加载资源-站点关联关系
  async loadResourceSiteRelations({ commit }, forceRefresh = false) {
    if (!forceRefresh && state.resourceSiteRelations.length > 0) {
      return state.resourceSiteRelations;
    }
    
    commit("SET_LOADING", { type: "resourceSiteRelations", loading: true });
    commit("CLEAR_ERROR", "resourceSiteRelations");
    
    try {
      const response = await getResourceSiteRelations();
      if (response.code === 0) {
        commit("SET_RESOURCE_SITE_RELATIONS", response.data || []);
        return response.data || [];
      } else {
        const error = response.msg || "加载资源-站点关联关系失败";
        commit("SET_ERROR", { type: "resourceSiteRelations", error });
        throw new Error(error);
      }
    } catch (error) {
      const errorMsg = error.message || "加载资源-站点关联关系失败";
      commit("SET_ERROR", { type: "resourceSiteRelations", error: errorMsg });
      console.error("加载资源-站点关联关系失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", { type: "resourceSiteRelations", loading: false });
    }
  },
  
  // 加载站点-设备关联关系
  async loadSiteDeviceRelations({ commit }, forceRefresh = false) {
    if (!forceRefresh && state.siteDeviceRelations.length > 0) {
      return state.siteDeviceRelations;
    }
    
    commit("SET_LOADING", { type: "siteDeviceRelations", loading: true });
    commit("CLEAR_ERROR", "siteDeviceRelations");
    
    try {
      const response = await getSiteDeviceRelations();
      if (response.code === 0) {
        commit("SET_SITE_DEVICE_RELATIONS", response.data || []);
        return response.data || [];
      } else {
        const error = response.msg || "加载站点-设备关联关系失败";
        commit("SET_ERROR", { type: "siteDeviceRelations", error });
        throw new Error(error);
      }
    } catch (error) {
      const errorMsg = error.message || "加载站点-设备关联关系失败";
      commit("SET_ERROR", { type: "siteDeviceRelations", error: errorMsg });
      console.error("加载站点-设备关联关系失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", { type: "siteDeviceRelations", loading: false });
    }
  },
  
  // 加载可用房间数据
  async loadAvailableRooms({ commit }, forceRefresh = false) {
    if (!forceRefresh && Object.keys(state.availableRooms).length > 0) {
      return state.availableRooms;
    }
    
    commit("SET_LOADING", { type: "availableRooms", loading: true });
    commit("CLEAR_ERROR", "availableRooms");
    
    try {
      const response = await getAvailableRooms();
      if (response.code === 0) {
        commit("SET_AVAILABLE_ROOMS", response.data || {});
        return response.data || {};
      } else {
        const error = response.msg || "加载可用房间数据失败";
        commit("SET_ERROR", { type: "availableRooms", error });
        throw new Error(error);
      }
    } catch (error) {
      const errorMsg = error.message || "加载可用房间数据失败";
      commit("SET_ERROR", { type: "availableRooms", error: errorMsg });
      console.error("加载可用房间数据失败:", error);
      throw error;
    } finally {
      commit("SET_LOADING", { type: "availableRooms", loading: false });
    }
  },
  
  // 加载所有基础配置数据
  async loadAllBaseConfig({ dispatch }) {
    const promises = [
      dispatch("loadGeographicalData"),
      dispatch("loadResourceSiteRelations"),
      dispatch("loadSiteDeviceRelations"),
      dispatch("loadAvailableRooms")
    ];
    
    try {
      await Promise.all(promises);
      console.log("所有基础配置数据加载完成");
    } catch (error) {
      console.error("加载基础配置数据时发生错误:", error);
      // 不抛出错误，允许部分数据加载失败
    }
  },
  
  // 刷新所有基础配置数据
  async refreshAllBaseConfig({ dispatch }) {
    const promises = [
      dispatch("loadGeographicalData", true),
      dispatch("loadResourceSiteRelations", true),
      dispatch("loadSiteDeviceRelations", true),
      dispatch("loadAvailableRooms", true)
    ];
    
    try {
      await Promise.all(promises);
      console.log("所有基础配置数据刷新完成");
    } catch (error) {
      console.error("刷新基础配置数据时发生错误:", error);
      throw error;
    }
  }
};

const getters = {
  // 获取地理数据
  geographicalData: state => state.geographicalData,
  
  // 获取省份列表
  provinces: state => {
    if (!state.geographicalData || !state.geographicalData.provinces) {
      return [];
    }
    return state.geographicalData.provinces;
  },
  
  // 获取城市列表
  cities: state => {
    if (!state.geographicalData || !state.geographicalData.cities) {
      return [];
    }
    return state.geographicalData.cities;
  },
  
  // 获取区县列表
  districts: state => {
    if (!state.geographicalData || !state.geographicalData.districts) {
      return [];
    }
    return state.geographicalData.districts;
  },
  
  // 根据省份ID获取城市列表
  getCitiesByProvinceId: state => provinceId => {
    if (!state.geographicalData || !state.geographicalData.cities) {
      return [];
    }
    return state.geographicalData.cities.filter(
      city => city.province_id === provinceId || city.province_code === provinceId
    );
  },
  
  // 根据城市ID获取区县列表
  getDistrictsByCityId: state => cityId => {
    if (!state.geographicalData || !state.geographicalData.districts) {
      return [];
    }
    return state.geographicalData.districts.filter(
      district => district.city_id === cityId || district.city_code === cityId
    );
  },
  
  // 获取资源-站点关联关系
  resourceSiteRelations: state => state.resourceSiteRelations,
  
  // 获取站点-设备关联关系
  siteDeviceRelations: state => state.siteDeviceRelations,
  
  // 获取可用房间数据
  availableRooms: state => state.availableRooms,
  
  // 获取加载状态
  isLoading: state => type => state.loading[type] || false,
  
  // 获取错误信息
  getError: state => type => state.errors[type],
  
  // 检查是否有任何加载中的数据
  hasAnyLoading: state => {
    return Object.values(state.loading).some(loading => loading);
  },
  
  // 检查是否有任何错误
  hasAnyError: state => {
    return Object.values(state.errors).some(error => error !== null);
  },
  
  // 获取数据加载时间戳
  getTimestamp: state => type => state.timestamps[type]
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
